import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
const MOCK_MODE = import.meta.env.VITE_MOCK_MODE === 'true';

// Log configuration for debugging
console.log('🔧 API Configuration:', {
  baseURL: API_BASE_URL,
  mockMode: MOCK_MODE,
  environment: import.meta.env.MODE
});

const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  // Log API calls in mock mode for debugging
  if (MOCK_MODE) {
    console.log('🌐 API Request:', {
      method: config.method?.toUpperCase(),
      url: config.url,
      baseURL: config.baseURL,
      hasAuth: !!token,
      data: config.data ? 'Present' : 'None'
    });
  }

  return config;
});

// Handle auth errors and log responses in mock mode
api.interceptors.response.use(
  (response) => {
    // Log API responses in mock mode for debugging
    if (MOCK_MODE) {
      console.log('✅ API Response:', {
        status: response.status,
        url: response.config.url,
        dataType: typeof response.data,
        success: response.data?.success,
        hasData: !!response.data?.data
      });
    }
    return response;
  },
  (error) => {
    if (MOCK_MODE) {
      console.error('❌ API Error:', {
        status: error.response?.status,
        url: error.config?.url,
        message: error.response?.data?.message || error.message
      });
    }

    if (error.response?.status === 401) {
      const requestUrl = error.config?.url || '';
      // Do not redirect for auth endpoints so errors can be shown on the form
      if (!requestUrl.includes('/auth/login') && !requestUrl.includes('/auth/signup')) {
        localStorage.removeItem('token');
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export interface AuthResponse {
  message: string;
  token: string;
  user: {
    id: string;
    email: string;
    username?: string;
    firstName?: string;
    lastName?: string;
  };
}

export interface LoginData {
  email: string;
  password: string;
}

export interface SignupData {
  email: string;
  password: string;
  username?: string;
  firstName?: string;
  lastName?: string;
}

// New schema types
export interface QuestionAnswer {
  [questionName: string]: string;
}

export interface ProjectData {
  technical: {
    [sectionName: string]: QuestionAnswer[];
  };
  functional: {
    [sectionName: string]: QuestionAnswer[];
  };
}

export interface UserData {
  id?: string;
  email?: string;
  prompt?: string;
}

export interface InterviewConfigData {
  user: UserData;
  projectData: ProjectData;
  uuid?: string;
}

export interface InterviewConfigResponse {
  success: boolean;
  message?: string;
  data: {
    uuid: string;
    name?: string;
    user: UserData;
    projectData: ProjectData;
    createdAt: string;
    updatedAt: string;
  };
}

// Auth API methods
export const authAPI = {
  login: async (credentials: LoginData): Promise<AuthResponse> => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  signup: async (userData: SignupData): Promise<AuthResponse> => {
    const response = await api.post('/auth/signup', userData);
    return response.data;
  },

  logout: async (): Promise<void> => {
    // For now, logout is handled client-side (JWT tokens)
    // But we call the endpoint in case we want to implement server-side logout later
    try {
      await api.post('/auth/logout');
    } catch (error) {
      // Ignore logout API errors as client-side cleanup is more important
      console.warn('Logout API call failed:', error);
    }
  }
};

// Interview API methods
export const interviewAPI = {
  save: async (data: InterviewConfigData): Promise<InterviewConfigResponse> => {
    const response = await api.post('/interview', data);
    return response.data;
  },

  get: async (uuid: string): Promise<InterviewConfigResponse> => {
    const response = await api.get(`/interview/${uuid}`);
    return response.data;
  },

  getUserHistory: async (userId: string, options?: {
    page?: number;
    limit?: number;
  }): Promise<{
    success: boolean;
    data: Array<{
      uuid: string;
      name?: string;
      prompt: string;
      createdAt: string;
      updatedAt: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
      totalPages: number;
    };
  }> => {
    const params = new URLSearchParams();
    if (options?.page) params.append('page', options.page.toString());
    if (options?.limit) params.append('limit', options.limit.toString());

    const queryString = params.toString();
    const url = `/interview/history/${userId}${queryString ? `?${queryString}` : ''}`;
    const response = await api.get(url);
    return response.data;
  },

  renameHistory: async (uuid: string, title: string): Promise<{
    success: boolean;
    data: { uuid: string; name?: string; prompt: string; createdAt: string; updatedAt: string };
  }> => {
    const response = await api.put(`/interview/${uuid}/title`, { title });
    return response.data;
  },

  deleteHistory: async (uuid: string): Promise<{ success: boolean; data: any } > => {
    const response = await api.delete(`/interview/${uuid}`);
    return response.data;
  }
};

// LLM API methods
export const llmAPI = {
  // Generate LLM response with content extraction
  generate: async (data: {
    interviewUuid?: string;
    prompt?: string;
    projectData?: any;
    userData?: any;
  }) => {
    const response = await api.post('/llm/generate', data);
    return response.data;
  },

  // Get build result by interview UUID
  getBuildResult: async (interviewUuid: string) => {
    const response = await api.get(`/llm/result/${interviewUuid}`);
    return response.data;
  },

  // Health check for LLM service
  healthCheck: async () => {
    const response = await api.get('/llm/health');
    return response.data;
  },

  // Check if build exists for interview UUID
  checkBuildExists: async (interviewUuid: string) => {
    const response = await api.get(`/llm/build-exists/${interviewUuid}`);
    return response.data;
  }
};

// User API methods
export const userAPI = {
	getMe: async (): Promise<{ success: boolean; data: { id: string; email: string; username?: string; firstName?: string; lastName?: string } }> => {
		const response = await api.get('/user/me');
		return response.data;
	},
	updateMe: async (data: { email?: string; firstName?: string; lastName?: string; username?: string }): Promise<{ success: boolean; message: string; data: { id: string; email: string; username?: string; firstName?: string; lastName?: string }; token?: string }> => {
		const response = await api.put('/user/me', data);
		return response.data;
	},
	updatePassword: async (data: { oldPassword: string; newPassword: string }): Promise<{ success: boolean; message: string }> => {
		const response = await api.put('/user/password', data);
		return response.data;
	},
};

// Build API methods
export const buildAPI = {
  // Unified initial build method (replaces llm.generate + build.archive)
  unifiedBuild: async (data: {
    interviewUuid?: string;
    prompt?: string;
    projectData?: any;
    userData?: any;
  }) => {
    const response = await api.post('/build/unified', data);
    return response.data;
  },

  // New method to get code structure without chat messages
  getCode: async (data: {
    interviewUuid?: string;
    prompt?: string;
    projectData?: any;
    userData?: any;
  }) => {
    const response = await api.post('/build/code', data);
    return response.data;
  },

  // Legacy method - redirected to unified build
  buildProject: async (data: {
    interviewUuid?: string;
    prompt?: string;
    projectData?: any;
    userData?: any;
  }) => {
    const response = await api.post('/build/unified', data);
    return response.data;
  },

  // Create ECS workspace for project
  createProjectWorkspace: async (data: {
    interviewUuid: string;
  }) => {
    return {
      success: true,
      workspace: {
        workspaceId: `workspace_${data.interviewUuid}`,
        status: 'created'
      },
      message: 'ECS workspace created successfully'
    };
  },

  // Updated method for streaming chat messages during generation
  generateCode: async (data: {
    interviewUuid?: string;
    prompt?: string;
    projectData?: any;
    userData?: any;
  }) => {
    // Use fetch for streaming but ensure auth header is included
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${API_BASE_URL}/api/build/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    });

    // Handle authentication errors
    if (response.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
      throw new Error('Authentication expired');
    }

    return response;
  },

  chat: async (data: {
    interviewUuid: string;
    message: string;
    conversationHistory?: any[];
  }) => {
    // Use fetch for streaming but ensure auth header is included
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${API_BASE_URL}/api/build/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    });

    // Handle authentication errors
    if (response.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
      throw new Error('Authentication expired');
    }

    return response;
  }
};

// Project API methods
export const projectAPI = {
  // Get project by interview UUID (creates if doesn't exist)
  getByInterview: async (interviewUuid: string) => {
    const response = await api.get(`/projects/interview/${interviewUuid}`);
    return response.data;
  },

  // Save/create a new project
  save: async (data: {
    interviewUuid?: string;
    name: string;
    description?: string;
    files?: Array<{
      path: string;
      content: string;
      language: string;
    }>;
  }) => {
    const response = await api.post('/projects', data);
    return response.data;
  },

  // Update a file in the project
  updateFile: async (projectUuid: string, data: {
    path: string;
    content: string;
    language: string;
  }) => {
    const response = await api.put(`/projects/${projectUuid}/files`, data);
    return response.data;
  },

  // Delete a file from the project
  deleteFile: async (projectUuid: string, path: string) => {
    const response = await api.delete(`/projects/${projectUuid}/files`, {
      data: { path }
    });
    return response.data;
  },

  // Get all projects for a user
  getUserProjects: async (userId: string) => {
    const response = await api.get(`/projects/user/${userId}`);
    return response.data;
  }
};

// Workspace API methods (Legacy local file system)
export const workspaceAPI = {
  // List all available workspaces
  listWorkspaces: async () => {
    const response = await api.get('/workspace');
    return response.data;
  },

  // Create a new workspace
  createWorkspace: async (workspaceName: string) => {
    const response = await api.post('/workspace', { workspaceName });
    return response.data;
  },

  // Get workspace file structure
  getStructure: async (workspaceName: string) => {
    const response = await api.get(`/workspace/${workspaceName}/structure`);
    return response.data;
  },

  // Read a specific file from workspace
  readFile: async (workspaceName: string, filePath: string) => {
    const response = await api.post(`/workspace/${workspaceName}/files/read`, { filePath });
    return response.data;
  },

  // Write/update a file in workspace
  writeFile: async (workspaceName: string, filePath: string, content: string) => {
    const response = await api.post(`/workspace/${workspaceName}/files/write`, { filePath, content });
    return response.data;
  },

  // Delete a file from workspace
  deleteFile: async (workspaceName: string, filePath: string) => {
    const response = await api.delete(`/workspace/${workspaceName}/files`, {
      data: { filePath }
    });
    return response.data;
  },

  // Start preview server for frontend
  startPreview: async (workspaceName: string) => {
    const response = await api.post(`/workspace/${workspaceName}/preview`);
    return response.data;
  },

  // Stop preview server
  stopPreview: async (workspaceName: string) => {
    const response = await api.delete(`/workspace/${workspaceName}/preview`);
    return response.data;
  }
};

// ECS Workspace API methods (New AWS ECS-based workspaces)
export const ecsWorkspaceAPI = {
  // Create a new ECS workspace
  createWorkspace: async (workspaceName: string, lifetimeHours?: number) => {
    const response = await api.post('/ecs-workspace', { workspaceName, lifetimeHours });
    return response.data;
  },

  // Get ECS workspace file structure
  getStructure: async (workspaceName: string) => {
    const response = await api.get(`/ecs-workspace/${workspaceName}/structure`);
    return response.data;
  },

  // Read a specific file from ECS workspace
  readFile: async (workspaceName: string, filePath: string) => {
    const response = await api.post(`/ecs-workspace/${workspaceName}/files/read`, { filePath });
    return response.data;
  },

  // Write/update a file in ECS workspace
  writeFile: async (workspaceName: string, filePath: string, content: string) => {
    const response = await api.post(`/ecs-workspace/${workspaceName}/files/write`, { filePath, content });
    return response.data;
  },

  // Delete a file from ECS workspace
  deleteFile: async (workspaceName: string, filePath: string) => {
    const response = await api.delete(`/ecs-workspace/${workspaceName}/files`, {
      data: { filePath }
    });
    return response.data;
  },

  // Create a folder in ECS workspace
  createFolder: async (workspaceName: string, folderPath: string) => {
    const response = await api.post(`/ecs-workspace/${workspaceName}/folders`, { folderPath });
    return response.data;
  },

  // Delete a folder from ECS workspace
  deleteFolder: async (workspaceName: string, folderPath: string) => {
    const response = await api.delete(`/ecs-workspace/${workspaceName}/folders`, {
      data: { folderPath }
    });
    return response.data;
  },

  // Rename a file or folder in ECS workspace
  renameItem: async (workspaceName: string, oldPath: string, newPath: string) => {
    const response = await api.post(`/ecs-workspace/${workspaceName}/rename`, { oldPath, newPath });
    return response.data;
  },

  // Save multiple files to ECS workspace
  saveFiles: async (workspaceName: string, files: { [filePath: string]: string }) => {
    const response = await api.post(`/ecs-workspace/${workspaceName}/save`, { files });
    return response.data;
  },

  // Extract files from LLM response and save to ECS workspace
  extractFiles: async (workspaceName: string, llmResponse: string) => {
    const response = await api.post(`/ecs-workspace/${workspaceName}/extract`, { llmResponse });
    return response.data;
  },

  // Start preview server for ECS workspace
  startPreview: async (workspaceName: string) => {
    const response = await api.post(`/ecs-workspace/${workspaceName}/preview`);
    return response.data;
  },

  // Stop preview server for ECS workspace
  stopPreview: async (workspaceName: string) => {
    const response = await api.delete(`/ecs-workspace/${workspaceName}/preview`);
    return response.data;
  },

  // Start publish for ECS workspace (ci->build->preview)
  startPublish: async (workspaceName: string, force?: boolean) => {
    const query = force ? '?force=true' : '';
    const response = await api.post(`/ecs-workspace/${workspaceName}/publish${query}`, force ? { force: true } : undefined);
    return response.data;
  },

  // Get ECS workspace container status
  getWorkspaceStatus: async (workspaceName: string) => {
    const response = await api.get(`/ecs-workspace/${workspaceName}/status`);
    return response.data;
  },

  // Get preview status for ECS workspace
  getPreviewStatus: async (workspaceName: string) => {
    const response = await api.get(`/ecs-workspace/${workspaceName}/preview/status`);
    return response.data;
  },

  // Get backend status for ECS workspace
  getBackendStatus: async (workspaceName: string) => {
    const response = await api.get(`/ecs-workspace/${workspaceName}/backend/status`);
    return response.data;
  }
};

// Config API methods
export const configAPI = {
  getPublicConfig: async () => {
    const response = await api.get('/config');
    return response.data;
  }
};

// Lifecycle API methods
export const lifecycleAPI = {
  getWorkspaceLifecycleInfo: async (workspaceId: string) => {
    const response = await api.get(`/lifecycle/workspace/${workspaceId}`);
    return response.data;
  },
  extendWorkspaceLifetime: async (workspaceId: string, hours?: number) => {
    const response = await api.post(`/lifecycle/workspace/${workspaceId}/extend`, { hours });
    return response.data;
  }
};

// Chat History API
export interface ChatMessage {
  id: string;
  type: 'ai' | 'user';
  content: string;
  timestamp: Date;
}

export const chatAPI = {
  // Get chat history for a build result
  getChatHistory: async (buildResultUuid: string) => {
    const response = await api.get(`/llm/chat/${buildResultUuid}`);
    return response.data;
  },

  // Update chat history for a build result
  updateChatHistory: async (buildResultUuid: string, chatHistory: ChatMessage[]) => {
    const response = await api.put(`/llm/chat/${buildResultUuid}`, { chatHistory });
    return response.data;
  },

  // Check if build exists for interview
  checkBuildExists: async (interviewUuid: string) => {
    const response = await api.get(`/llm/build-exists/${interviewUuid}`);
    return response.data;
  }
};

// Conversation Context API
export const conversationAPI = {
  // Save a single conversation message
  saveMessage: async (data: {
    interviewUuid: string;
    messageType: 'user' | 'assistant';
    content: string;
    inputTokens?: number;
    outputTokens?: number;
    totalTokens?: number;
    model?: string;
    parentMessageId?: string;
  }) => {
    const response = await api.post('/conversation/message', data);
    return response.data;
  },

  // Save a conversation exchange (user + assistant)
  saveExchange: async (data: {
    interviewUuid: string;
    userMessage: string;
    assistantMessage: string;
    metadata?: {
      inputTokens?: number;
      outputTokens?: number;
      totalTokens?: number;
      model?: string;
    };
  }) => {
    const response = await api.post('/conversation/exchange', data);
    return response.data;
  },

  // Get conversation context
  getContext: async (interviewUuid: string, maxMessages?: number) => {
    const params = maxMessages ? `?maxMessages=${maxMessages}` : '';
    const response = await api.get(`/conversation/context/${interviewUuid}${params}`);
    return response.data;
  },

  // Get complete conversation context for LLM API
  getCompleteContext: async (interviewUuid: string, currentUserMessage: string) => {
    const response = await api.post(`/conversation/context/${interviewUuid}/complete`, {
      currentUserMessage
    });
    return response.data;
  },

  // Get conversation statistics
  getStats: async (interviewUuid: string) => {
    const response = await api.get(`/conversation/stats/${interviewUuid}`);
    return response.data;
  },

  // Clear conversation history
  clearHistory: async (interviewUuid: string) => {
    const response = await api.delete(`/conversation/${interviewUuid}`);
    return response.data;
  }
};

export default api;