import axios, { AxiosResponse } from 'axios';
import { Workspace } from '../models/Workspace';
import { ECSContainer } from '../models/ECSContainer';
import { S3FileService } from './s3FileService';
import { log } from '../utils/logger';
import { getAWSConfig } from '../config/aws';
import { getAppConfig } from '../config/app';

export interface FileSystemFile {
  path: string;
  content: string;
  language: string;
  isDirectory: boolean;
  lastModified: Date;
}

export interface FileSystemItem {
  type: 'file' | 'directory' | 'folder';
  name: string;
  path: string;
  size?: number;
  language?: string;
  lastModified?: Date;
  children?: FileSystemItem[];
}

export interface FileSystemStructure {
  [key: string]: FileSystemItem;
}

export interface FileOperationResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export class ECSFileSystemService {
  private s3Service: S3FileService;
  private config = getAWSConfig();

  constructor() {
    this.s3Service = new S3FileService();
  }

  // Helper to generate concise HTTP/Axios error summaries for logs
  private summarizeHttpError(error: any): string {
    try {
      const code = error?.code || error?.response?.status || 'UNKNOWN';
      const statusText = error?.response?.statusText;
      const method = error?.config?.method?.toUpperCase();
      const url = error?.config?.url;
      const timeout = error?.config?.timeout;
      const message = error?.message;
      const details: string[] = [];
      if (method && url) details.push(`${method} ${url}`);
      if (timeout) details.push(`timeout ${timeout}ms`);
      if (statusText) details.push(statusText);
      if (message && !String(message).includes(String(code))) details.push(message);
      return `[${code}] ${details.join(' | ')}`;
    } catch {
      return String(error?.message || error);
    }
  }

  /**
   * Auto-extend workspace lifetime if it is within the threshold window before expiry
   */
  private async maybeExtendWorkspaceLifetime(
    workspaceId: string,
    thresholdMinutes: number = 15,
    extendHours?: number,
    context: 'user' | 'system' = 'user'
  ): Promise<void> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace || !workspace.expiresAt) {
        return;
      }

      const now = Date.now();
      const msLeft = workspace.expiresAt.getTime() - now;
      const thresholdMs = thresholdMinutes * 60 * 1000;
      const hoursToExtend = extendHours ?? this.config.extendLifetimeHours;

      if (msLeft > 0 && msLeft <= thresholdMs) {
        await workspace.extendLifetime(hoursToExtend);

        const container = await ECSContainer.findOne({ workspaceId });
        if (container) {
          await container.extendLifetime(hoursToExtend);
          await container.addEvent('started', `Lifetime extended by ${hoursToExtend} hours`);
        }

        log.info(`[LIFETIME][${context.toUpperCase()}] Auto-extended workspace ${workspaceId} by ${hoursToExtend}h (was ${Math.ceil(msLeft / 60000)}m left)`);
      }
    } catch (e) {
      log.warning(`[LIFETIME][${context.toUpperCase()}] Failed to maybe-extend workspace lifetime for ${workspaceId}:`, String((e as any)?.message || e));
    }
  }

  /**
   * Ensure S3 storage metadata exists for a workspace and return userEmail
   */
  private async ensureWorkspaceS3Storage(workspace: any): Promise<string> {
    if (workspace.s3FileStorage?.userEmail) {
      return workspace.s3FileStorage.userEmail;
    }
    const { User } = await import('../models/User');
    const owner = await User.findById(workspace.userId);
    const userEmail = owner?.email || 'unknown@unknown';
    const cleanWorkspaceName = workspace.interviewUuid.replace(/^workspace_/, '');
    workspace.s3FileStorage = {
      userEmail,
      s3Prefix: `${userEmail}/${cleanWorkspaceName}/`,
      lastSavedAt: new Date(),
      version: 0,
      fileCount: 0,
      totalSize: 0,
      initialFilesUploaded: false
    } as any;
    await workspace.save();
    return userEmail;
  }

  /**
   * Get workspace container URL
   */
  private async getContainerUrl(workspaceId: string): Promise<string> {
    log.verbose('Getting container URL for workspace:', workspaceId);

    const workspace = await Workspace.findById(workspaceId);

    log.verbose('Workspace details:', {
      found: !!workspace,
      publicIp: workspace?.publicIp,
      containerStatus: workspace?.containerStatus,
      containerPort: workspace?.containerConfig?.port
    });

    if (!workspace) {
      throw new Error('Workspace not found');
    }

    if (!workspace.publicIp) {
      throw new Error('Workspace container not ready - no public IP available');
    }

    if (workspace.containerStatus !== 'running') {
      throw new Error(`Workspace container not ready - status: ${workspace.containerStatus}`);
    }

    const containerUrl = `http://${workspace.publicIp}:${workspace.containerConfig.port}`;
    log.verbose('Container URL:', containerUrl);
    return containerUrl;
  }

  /**
   * Get workspace file structure
   */
  async getWorkspaceStructure(workspaceId: string, options?: { skipAutoExtend?: boolean }): Promise<FileSystemStructure> {
    try {
      const containerUrl = await this.getContainerUrl(workspaceId);
      
      const response: AxiosResponse = await axios.get(`${containerUrl}/`, {
        timeout: getAppConfig().workspaceTimeoutMs
      });

      // Attempt auto-extension on browse (unless explicitly skipped)
      if (!options?.skipAutoExtend) {
        await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');
      }

      if (response.data && response.data.files) {
        return this.convertToWorkspaceStructure(response.data.files);
      }

      return {};
    } catch (error: any) {
      log.failure('Error getting workspace structure:', this.summarizeHttpError(error));
      
      // Fallback to S3 files if container is not available
      const workspace = await Workspace.findById(workspaceId);
      if (workspace?.s3FileStorage) {
        const s3FilesResult = await this.s3Service.listWorkspaceFiles(
          workspace.s3FileStorage.userEmail, 
          workspace.interviewUuid
        );
        
        if (s3FilesResult.success && s3FilesResult.data?.files) {
          // Attempt auto-extension on browse via S3 fallback (unless explicitly skipped)
          if (!options?.skipAutoExtend) {
            await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');
          }
          return this.convertS3FilesToStructure(s3FilesResult.data.files);
        }
      }
      
      throw new Error(`Failed to get workspace structure: ${error.message}`);
    }
  }

  /**
   * Read a file from workspace
   */
  async readFile(workspaceId: string, filePath: string, options?: { skipAutoExtend?: boolean }): Promise<FileSystemFile> {
    try {
      const containerUrl = await this.getContainerUrl(workspaceId);

      log.verbose('Reading file:', filePath, 'from container:', containerUrl);

      // Use the correct container endpoint: /files/<path>
      const response: AxiosResponse = await axios.get(`${containerUrl}/files/${encodeURIComponent(filePath)}`, {
        timeout: getAppConfig().workspaceTimeoutMs
      });

      // Attempt auto-extension on browse (unless explicitly skipped)
      if (!options?.skipAutoExtend) {
        await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');
      }

      // Container returns plain text content directly
      if (response.status === 200) {
        return {
          path: filePath,
          content: response.data, // Plain text content
          language: this.getLanguageFromFilename(filePath),
          isDirectory: false,
          lastModified: new Date() // Container doesn't provide lastModified
        };
      }

      throw new Error('Failed to read file');
    } catch (error: any) {
      log.failure('Error reading file from container:', filePath, this.summarizeHttpError(error));
      
      // Fallback to S3 file
      const workspace = await Workspace.findById(workspaceId);
      if (workspace?.s3FileStorage) {
        const s3FileResult = await this.s3Service.downloadFile(
          workspace.s3FileStorage.userEmail,
          workspace.interviewUuid,
          filePath
        );
        
        if (s3FileResult.success && s3FileResult.data?.content) {
          // Attempt auto-extension on browse via S3 fallback (unless explicitly skipped)
          if (!options?.skipAutoExtend) {
            await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');
          }
          return {
            path: filePath,
            content: s3FileResult.data.content,
            language: this.getLanguageFromFilename(filePath),
            isDirectory: false,
            lastModified: s3FileResult.data.lastModified || workspace.s3FileStorage.lastSavedAt
          };
        }
      }
      
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }

  /**
   * Write a file to workspace
   */
  async writeFile(workspaceId: string, filePath: string, content: string): Promise<FileOperationResult> {
    try {
      const containerUrl = await this.getContainerUrl(workspaceId);

      log.verbose('Writing file:', filePath, 'to container:', containerUrl);

      // Use the correct container endpoint: PUT /files/<path> with content as body
      const response: AxiosResponse = await axios.put(`${containerUrl}/files/${encodeURIComponent(filePath)}`, content, {
        headers: {
          'Content-Type': 'text/plain'
        },
        timeout: 15000
      });

      if (response.status === 200 && response.data && response.data.success) {
        // Update workspace last accessed time and sync to S3
        const workspace = await Workspace.findById(workspaceId);
        if (workspace) {
          await workspace.updateLastAccessed();
          // Sync file to S3 for persistence
          const filesToSync: { [p: string]: string } = { [filePath]: content };
          const userEmail = await this.ensureWorkspaceS3Storage(workspace);
          await this.syncFilesToS3(workspaceId, filesToSync, userEmail);
        }

        // Attempt auto-extension on edit
        await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');

        return {
          success: true,
          message: 'File saved successfully',
          data: {
            path: filePath,
            content,
            language: this.getLanguageFromFilename(filePath)
          }
        };
      }

      throw new Error(response.data?.error || 'Failed to write file');
    } catch (error: any) {
      log.failure('Error writing file to container:', filePath, this.summarizeHttpError(error));

      // Save to S3 as fallback
      try {
        const workspace = await Workspace.findById(workspaceId);
        if (workspace) {
          const userEmail = await this.ensureWorkspaceS3Storage(workspace);
          const newFiles = { [filePath]: content };
          await workspace.saveFilesToS3(newFiles, userEmail, true);

          // Attempt auto-extension on edit via S3 fallback
          await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');

          return {
            success: true,
            message: 'File saved to S3 (container unavailable)',
            data: {
              path: filePath,
              content,
              language: this.getLanguageFromFilename(filePath)
            }
          };
        }
      } catch (e: any) {
        log.failure('Error saving file to S3 fallback:', filePath, String((e as any)?.message || e));
      }

      return {
        success: false,
        message: 'Failed to write file'
      };
    }
  }

  /**
   * Delete a file from workspace
   */
  async deleteFile(workspaceId: string, filePath: string): Promise<FileOperationResult> {
    try {
      const containerUrl = await this.getContainerUrl(workspaceId);

      log.verbose('Deleting file from container:', filePath, containerUrl);

      // Use the correct container endpoint: DELETE /files/<path>
      const response: AxiosResponse = await axios.delete(`${containerUrl}/files/${encodeURIComponent(filePath)}`, {
        timeout: getAppConfig().workspaceTimeoutMs
      });

      if (response.status === 200 && response.data && response.data.success) {
        // Update workspace last accessed time
        const workspace = await Workspace.findById(workspaceId);
        if (workspace) {
          await workspace.updateLastAccessed();

          // Also delete from S3 storage
          if (workspace.s3FileStorage) {
            try {
              await this.s3Service.deleteFile(
                workspace.s3FileStorage.userEmail,
                workspace.interviewUuid,
                filePath
              );
              log.verbose('File also deleted from S3:', filePath);
            } catch (s3Error) {
              log.warning('Failed to delete file from S3 (continuing anyway):', filePath, String(s3Error));
            }
          }
        }

        // Attempt auto-extension on edit
        await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');

        return {
          success: true,
          message: 'File deleted successfully'
        };
      }

      throw new Error(response.data?.error || 'Failed to delete file');
    } catch (error: any) {
      log.failure('Error deleting file from container:', filePath, this.summarizeHttpError(error));
      return {
        success: false,
        message: 'Failed to delete file',
        error: error.message
      };
    }
  }

  /**
   * Create a folder in workspace
   */
  async createFolder(workspaceId: string, folderPath: string): Promise<FileOperationResult> {
    try {
      const containerUrl = await this.getContainerUrl(workspaceId);

      log.verbose('Creating folder in container:', folderPath, containerUrl);

      // Since the container doesn't have a dedicated folder creation endpoint,
      // we create a folder by creating a placeholder file inside it and then deleting it
      const placeholderPath = `${folderPath}/.gitkeep`;

      // Create placeholder file to ensure directory exists
      const response: AxiosResponse = await axios.put(`${containerUrl}/files/${encodeURIComponent(placeholderPath)}`, '', {
        headers: {
          'Content-Type': 'text/plain'
        },
        timeout: getAppConfig().workspaceTimeoutMs
      });

      if (response.status === 200 && response.data && response.data.success) {
        // Update workspace last accessed time
        const workspace = await Workspace.findById(workspaceId);
        if (workspace) {
          await workspace.updateLastAccessed();
        }

        // Attempt auto-extension on edit
        await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');

        return {
          success: true,
          message: 'Folder created successfully',
          data: {
            path: folderPath,
            type: 'folder'
          }
        };
      }

      throw new Error(response.data?.error || 'Failed to create folder');
    } catch (error: any) {
      log.failure('Error creating folder in container:', folderPath, this.summarizeHttpError(error));
      return {
        success: false,
        message: 'Failed to create folder',
        error: error.message
      };
    }
  }

  /**
   * Delete a folder from workspace
   */
  async deleteFolder(workspaceId: string, folderPath: string): Promise<FileOperationResult> {
    try {
      // Since the container doesn't have a dedicated folder deletion endpoint,
      // we need to get the workspace structure and delete all files in the folder
      const structure = await this.getWorkspaceStructure(workspaceId, { skipAutoExtend: true });

      // Find all files that start with the folder path
      const filesToDelete: string[] = [];
      this.collectFilesInFolder(structure, folderPath, filesToDelete);

      if (filesToDelete.length === 0) {
        return {
          success: true,
          message: 'Folder is empty or does not exist'
        };
      }

      log.verbose('Deleting folder contents:', folderPath, 'files:', filesToDelete);

      // Delete all files in the folder
      const deletePromises = filesToDelete.map(filePath => this.deleteFile(workspaceId, filePath));
      const results = await Promise.all(deletePromises);

      const failedDeletes = results.filter(result => !result.success);
      if (failedDeletes.length > 0) {
        return {
          success: false,
          message: `Failed to delete ${failedDeletes.length} files in folder`,
          error: failedDeletes.map(r => r.message).join(', ')
        };
      }

      // Update workspace last accessed time
      const workspace = await Workspace.findById(workspaceId);
      if (workspace) {
        await workspace.updateLastAccessed();
      }

      // Attempt auto-extension on edit
      await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');

      return {
        success: true,
        message: `Folder deleted successfully (${filesToDelete.length} files removed)`
      };
    } catch (error: any) {
      log.failure('Error deleting folder from container:', folderPath, this.summarizeHttpError(error));
      return {
        success: false,
        message: 'Failed to delete folder',
        error: error.message
      };
    }
  }

  /**
   * Helper method to collect all files in a folder recursively
   */
  private collectFilesInFolder(structure: any, folderPath: string, filesToDelete: string[], currentPath: string = ''): void {
    if (!structure || typeof structure !== 'object') return;

    for (const [name, item] of Object.entries(structure)) {
      const itemPath = currentPath ? `${currentPath}/${name}` : name;

      if (item && typeof item === 'object') {
        if ((item as any).type === 'folder') {
          // If this folder matches our target folder or is inside it, collect all its files
          if (itemPath === folderPath || itemPath.startsWith(folderPath + '/')) {
            this.collectFilesInFolder((item as any).children || {}, folderPath, filesToDelete, itemPath);
          } else if (folderPath.startsWith(itemPath + '/')) {
            // If our target folder is inside this folder, recurse into it
            this.collectFilesInFolder((item as any).children || {}, folderPath, filesToDelete, itemPath);
          }
        } else if ((item as any).type === 'file') {
          // If this file is in the target folder, add it to deletion list
          if (itemPath.startsWith(folderPath + '/') || itemPath === folderPath) {
            filesToDelete.push(itemPath);
          }
        }
      }
    }
  }

  /**
   * Rename a file or folder in workspace
   */
  async renameItem(workspaceId: string, oldPath: string, newPath: string): Promise<FileOperationResult> {
    try {
      log.verbose('Renaming item in container:', oldPath, '->', newPath);

      // Since the container doesn't have a dedicated rename endpoint,
      // we need to implement rename by copying content and deleting the original

      // First, determine if this is a file or folder by checking the workspace structure
      const structure = await this.getWorkspaceStructure(workspaceId, { skipAutoExtend: true });
      const itemInfo = this.findItemInStructure(structure, oldPath);

      if (!itemInfo) {
        return {
          success: false,
          message: 'Item not found',
          error: `No file or folder found at path: ${oldPath}`
        };
      }

      if (itemInfo.type === 'file') {
        // Rename a file: read content, write to new location, delete old
        const fileContent = await this.readFile(workspaceId, oldPath, { skipAutoExtend: true });
        const writeResult = await this.writeFile(workspaceId, newPath, fileContent.content);

        if (!writeResult.success) {
          return {
            success: false,
            message: 'Failed to create file at new location',
            error: writeResult.error
          };
        }

        const deleteResult = await this.deleteFile(workspaceId, oldPath);
        if (!deleteResult.success) {
          // Try to clean up the new file if deletion of old file failed
          await this.deleteFile(workspaceId, newPath);
          return {
            success: false,
            message: 'Failed to delete original file',
            error: deleteResult.error
          };
        }
      } else {
        // Rename a folder: collect all files, recreate them with new paths, delete originals
        const filesToMove: string[] = [];
        this.collectFilesInFolder(structure, oldPath, filesToMove);

        if (filesToMove.length === 0) {
          // Empty folder, just create the new folder
          const createResult = await this.createFolder(workspaceId, newPath);
          if (!createResult.success) {
            return createResult;
          }
        } else {
          // Move all files to new locations
          for (const filePath of filesToMove) {
            const relativePath = filePath.substring(oldPath.length + 1);
            const newFilePath = `${newPath}/${relativePath}`;

            try {
              const fileContent = await this.readFile(workspaceId, filePath, { skipAutoExtend: true });
              const writeResult = await this.writeFile(workspaceId, newFilePath, fileContent.content);

              if (!writeResult.success) {
                throw new Error(`Failed to create file at ${newFilePath}: ${writeResult.error}`);
              }
            } catch (error) {
              return {
                success: false,
                message: `Failed to move file ${filePath}`,
                error: String(error)
              };
            }
          }

          // Delete all original files
          const deleteResult = await this.deleteFolder(workspaceId, oldPath);
          if (!deleteResult.success) {
            return {
              success: false,
              message: 'Failed to delete original folder',
              error: deleteResult.error
            };
          }
        }
      }

      // Update workspace last accessed time
      const workspace = await Workspace.findById(workspaceId);
      if (workspace) {
        await workspace.updateLastAccessed();
      }

      // Attempt auto-extension on edit
      await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');

      return {
        success: true,
        message: 'Item renamed successfully',
        data: {
          oldPath,
          newPath
        }
      };
    } catch (error: any) {
      log.failure('Error renaming item in container:', oldPath, '->', newPath, this.summarizeHttpError(error));
      return {
        success: false,
        message: 'Failed to rename item',
        error: error.message
      };
    }
  }

  /**
   * Helper method to find an item in the workspace structure
   */
  private findItemInStructure(structure: any, targetPath: string): { type: 'file' | 'folder' } | null {
    if (!structure || typeof structure !== 'object') return null;

    const pathParts = targetPath.split('/');
    let current = structure;

    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];
      if (!current[part]) return null;

      current = current[part];
      if (i === pathParts.length - 1) {
        // This is the target item
        return { type: (current as any).type === 'folder' ? 'folder' : 'file' };
      }

      // Navigate deeper
      if ((current as any).type === 'folder') {
        current = (current as any).children || {};
      } else {
        return null; // Path goes through a file, which is invalid
      }
    }

    return null;
  }

  /**
   * Save multiple files to workspace
   */
  async saveFiles(workspaceId: string, files: { [filePath: string]: string }): Promise<FileOperationResult> {
    try {
      const containerUrl = await this.getContainerUrl(workspaceId);

      log.debug(`Saving ${Object.keys(files).length} files to container:`, containerUrl);

      // Save files individually using PUT /files/{filePath} since container doesn't have batch endpoint
      const savedFiles: string[] = [];
      const errors: string[] = [];

      for (const [filePath, content] of Object.entries(files)) {
        try {
          log.verbose(`Saving file: ${filePath}`);

          const response: AxiosResponse = await axios.put(`${containerUrl}/files/${encodeURIComponent(filePath)}`, content, {
            headers: {
              'Content-Type': 'text/plain'
            },
            timeout: getAppConfig().workspaceTimeoutMs
          });

          if (response.status === 200) {
            savedFiles.push(filePath);
            log.verbose(`Successfully saved: ${filePath}`);
          } else {
            errors.push(`${filePath}: HTTP ${response.status}`);
            log.failure(`Failed to save ${filePath}: HTTP ${response.status}`);
          }
        } catch (fileError: any) {
          errors.push(`${filePath}: ${fileError.message}`);
          log.failure(`Error saving ${filePath}:`, this.summarizeHttpError(fileError));
        }
      }

      // If we saved at least some files successfully, consider it a success
      if (savedFiles.length > 0) {
        // Update workspace last accessed time and save to S3
        const workspace = await Workspace.findById(workspaceId);
        if (workspace) {
          await workspace.updateLastAccessed();
          // Sync successfully saved files to S3 for persistence
          if (savedFiles.length > 0) {
            const filesToSync: { [filePath: string]: string } = {};
            for (const filePath of savedFiles) {
              filesToSync[filePath] = files[filePath];
            }
            const userEmail = await this.ensureWorkspaceS3Storage(workspace);
            await this.syncFilesToS3(workspaceId, filesToSync, userEmail);
          }
        }

        // Attempt auto-extension on edit
        await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');

        const message = errors.length > 0
          ? `Successfully saved ${savedFiles.length}/${Object.keys(files).length} files (${errors.length} failed)`
          : `Successfully saved ${savedFiles.length} files`;

        return {
          success: true,
          message,
          data: {
            savedFiles,
            count: savedFiles.length,
            errors: errors.length > 0 ? errors : undefined
          }
        };
      }

      // If no files were saved, throw error to trigger fallback
      throw new Error(`Failed to save any files. Errors: ${errors.join(', ')}`);
    } catch (error: any) {
      log.failure('Error saving files to container:', this.summarizeHttpError(error));

      // Save to S3 as fallback
      try {
        const workspace = await Workspace.findById(workspaceId);
        if (workspace) {
          const userEmail = await this.ensureWorkspaceS3Storage(workspace);
          await workspace.saveFilesToS3(files, userEmail, true);

          // Attempt auto-extension on edit via S3 fallback
          await this.maybeExtendWorkspaceLifetime(workspaceId, 15, this.config.extendLifetimeHours, 'user');

          return {
            success: true,
            message: 'Files saved to S3 (container unavailable)',
            data: {
              savedFiles: Object.keys(files),
              count: Object.keys(files).length
            }
          };
        }
      } catch (s3Error) {
        log.failure('Error saving files to S3 fallback:', String((s3Error as any)?.message || s3Error));
      }

      return {
        success: false,
        message: 'Failed to save files',
        error: error.message
      };
    }
  }

  /**
   * Sync files to S3 for persistence
   */
  private async syncFilesToS3(workspaceId: string, files: { [filePath: string]: string }, userEmail: string): Promise<void> {
    try {
      log.sync(`Syncing ${Object.keys(files).length} files to S3 for workspace:`, workspaceId);

      const workspace = await Workspace.findById(workspaceId);
      if (!workspace) {
        log.failure('Workspace not found for S3 sync:', workspaceId);
        return;
      }

      // Use merging to preserve existing files
      await workspace.saveFilesToS3(files, userEmail, true);

      log.success(`Successfully synced ${Object.keys(files).length} files to S3`);
    } catch (error: any) {
      log.failure('Error syncing files to S3:', String(error?.message || error));
      // Don't throw - this is a background sync operation
    }
  }

  /**
   * Restore files from S3 to container
   */
  async restoreFromS3(workspaceId: string): Promise<FileOperationResult> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace?.s3FileStorage) {
        return {
          success: false,
          message: 'No S3 storage available to restore'
        };
      }

      const s3FilesResult = await this.s3Service.downloadWorkspaceFiles(
        workspace.s3FileStorage.userEmail,
        workspace.interviewUuid
      );

      if (!s3FilesResult.success || !s3FilesResult.data?.files) {
        return {
          success: false,
          message: 'No files found in S3 to restore'
        };
      }

      const result = await this.saveFiles(workspaceId, s3FilesResult.data.files);

      if (result.success) {
        log.success(`Restored ${Object.keys(s3FilesResult.data.files).length} files from S3`);
      }

      return result;
    } catch (error: any) {
      log.failure('Error restoring from S3:', String(error?.message || error));
      return {
        success: false,
        message: 'Failed to restore from snapshot',
        error: error.message
      };
    }
  }

  /**
   * Check if workspace container is healthy
   */
  async checkHealth(workspaceId: string): Promise<boolean> {
    try {
      const containerUrl = await this.getContainerUrl(workspaceId);
      
      const response: AxiosResponse = await axios.get(`${containerUrl}/health`, {
        timeout: 5000
      });

      return response.data && response.data.status === 'healthy';
    } catch (error) {
      return false;
    }
  }

  /**
   * Convert S3 file list to workspace structure
   */
  private convertS3FilesToStructure(s3Files: any[]): FileSystemStructure {
    const structure: FileSystemStructure = {};

    for (const s3File of s3Files) {
      const filePath = s3File.key;
      const fileName = filePath.split('/').pop() || filePath;

      structure[filePath] = {
        type: 'file',
        name: fileName,
        path: filePath,
        size: s3File.size || 0,
        language: this.getLanguageFromFilename(fileName),
        lastModified: s3File.lastModified || new Date()
      };
    }

    return structure;
  }

  /**
   * Convert container file list to workspace structure
   */
  private convertToWorkspaceStructure(files: any[]): FileSystemStructure {
    const structure: FileSystemStructure = {};

    // Process files and build a flat structure with proper nesting
    for (const file of files) {
      const fileName = file.name;

      if (file.type === 'directory') {
        structure[fileName] = {
          type: 'folder',
          name: fileName,
          path: fileName,
          children: this.processChildrenRecursively(file.children || [], fileName)
        };
      } else {
        structure[fileName] = {
          type: 'file',
          name: fileName,
          path: fileName,
          size: file.size || 0,
          language: this.getLanguageFromFilename(fileName),
          lastModified: file.modified ? new Date(file.modified) : new Date()
        };
      }
    }

    return structure;
  }

  /**
   * Helper method to process children recursively
   */
  private processChildrenRecursively(children: any[], parentPath: string = ''): FileSystemItem[] {
    const result: FileSystemItem[] = [];

    for (const child of children) {
      const childName = child.name;
      const childPath = parentPath ? `${parentPath}/${childName}` : childName;

      if (child.type === 'directory') {
        result.push({
          type: 'folder',
          name: childName,
          path: childPath,
          children: this.processChildrenRecursively(child.children || [], childPath)
        });
      } else {
        result.push({
          type: 'file',
          name: childName,
          path: childPath,
          size: child.size || 0,
          language: this.getLanguageFromFilename(childName),
          lastModified: child.modified ? new Date(child.modified) : new Date()
        });
      }
    }

    return result;
  }

  /**
   * Get programming language from filename
   */
  private getLanguageFromFilename(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown',
      'sh': 'bash',
      'sql': 'sql'
    };
    
    return languageMap[ext || ''] || 'plaintext';
  }
}
